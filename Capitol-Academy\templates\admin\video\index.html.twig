{% extends 'admin/base.html.twig' %}

{% block title %}Video Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Video Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Videos</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Video Management',
    'page_icon': 'fas fa-video',
    'search_placeholder': 'Search videos by title, category, or description...',
    'create_button': {
        'url': path('admin_video_new'),
        'text': 'Add New Video',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Videos',
            'value': videos|length,
            'icon': 'fas fa-video',
            'color': '#1e3c72',
            'gradient': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
        },
        {
            'title': 'Free Videos',
            'value': videos|filter(video => video.isFree)|length,
            'icon': 'fas fa-unlock',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Premium Videos',
            'value': videos|filter(video => not video.isFree)|length,
            'icon': 'fas fa-crown',
            'color': '#ffc107',
            'gradient': 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)'
        },
        {
            'title': 'Active Videos',
            'value': videos|filter(video => video.isActive)|length,
            'icon': 'fas fa-play-circle',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}

        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Thumbnail'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Source'},
            {'text': 'Access Level'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for video in videos %}
            {% set row_cells = [
                {
                    'content': video.thumbnail ?
                        '<img src="/uploads/videos/thumbnails/' ~ video.thumbnail ~ '" alt="' ~ video.title ~ '" class="img-thumbnail" style="width: 60px; height: 40px; object-fit: cover;">' :
                        '<div class="bg-light d-flex align-items-center justify-content-center" style="width: 60px; height: 40px; border-radius: 4px;"><i class="fas fa-video text-muted"></i></div>'
                },
                {
                    'content': '<h6 class="video-title mb-0 font-weight-bold text-dark">' ~ video.title ~ '</h6>'
                },
                {
                    'content': video.category ?
                        '<span class="badge bg-info">' ~ video.category ~ '</span>' :
                        '<span class="text-muted">No category</span>'
                },
                {
                    'content':
                        (video.videoSourceType == 'youtube') ? '<span class="badge bg-danger"><i class="fab fa-youtube mr-1"></i>YouTube</span>' :
                        (video.videoSourceType == 'vdocipher') ? '<span class="badge bg-success"><i class="fas fa-shield-alt mr-1"></i>VdoCipher</span>' :
                        '<span class="badge bg-primary"><i class="fas fa-upload mr-1"></i>Upload</span>'
                },
                {
                    'content':
                        (video.accessLevel == 'public_free') ? '<span class="badge bg-success"><i class="fas fa-globe mr-1"></i>Public Free</span>' :
                        (video.accessLevel == 'login_required_free') ? '<span class="badge bg-info"><i class="fas fa-user mr-1"></i>Login Required</span>' :
                        '<span class="badge bg-warning"><i class="fas fa-crown mr-1"></i>Premium</span>'
                },
                {
                    'content': video.formattedPrice
                },
                {
                    'content': '<span class="badge ' ~ (video.isActive ? 'bg-success' : 'bg-secondary') ~ '">' ~ (video.isActive ? 'Active' : 'Inactive') ~ '</span>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_video_show', {'id': video.id}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Video"><i class="fas fa-eye"></i></a>
                        <a href="' ~ path('admin_video_edit', {'id': video.id}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Edit Video"><i class="fas fa-edit"></i></a>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, ' ~ (video.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (video.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (video.isActive ? 'Deactivate' : 'Activate') ~ ' Video" onclick="toggleVideoStatus(\'' ~ video.id ~ '\', \'' ~ video.title|e('js') ~ '\', ' ~ (video.isActive ? 'true' : 'false') ~ ')"><i class="fas fa-' ~ (video.isActive ? 'pause' : 'play') ~ '"></i></button>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Video" onclick="deleteVideo(\'' ~ video.id ~ '\', \'' ~ video.title|e('js') ~ '\')"><i class="fas fa-trash"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells, 'class': 'video-row'}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'video-row',
            'empty_message': 'No videos found',
            'empty_icon': 'fas fa-video',
            'empty_description': 'Get started by creating your first video.',
            'search_config': {
                'fields': ['.video-title']
            }
        } %}
    {% endblock %}
{% endembed %}


{% endblock %}

{% block javascripts %}
<script>
// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Video management functions
function toggleVideoStatus(videoId, videoTitle, currentStatus) {
    showStatusModal(videoTitle, currentStatus, function() {
        executeVideoStatusToggle(videoId);
    });
}

function deleteVideo(videoId, videoTitle) {
    showDeleteModal(videoTitle, function() {
        executeVideoDelete(videoId);
    });
}

// Actual execution functions
function executeVideoStatusToggle(videoId) {
    fetch(`/admin/videos/${videoId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the video status');
    });
}

function executeVideoDelete(videoId) {
    fetch(`/admin/videos/${videoId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the video');
    });
}
</script>
{% endblock %}
